"use client";

import { createClient } from "../../supabase/client";
import { toast } from "sonner";
import { getCurrentWord, setCurrentWordId, setMatchLock, updateMatchCurrentState } from "./database";
import { currentWord, Difficulty, Player } from "@/interfaces/interfaces";
import { calculateTimeLeft, resetStartTime } from "./waiting-utils";
import { useRouter } from "next/navigation";

const supabase = createClient();

export const useSupabase = () => {
    return supabase
}

interface Word {
    text: string;
    difficulty: Difficulty;
}

// Mock data for demonstration
export const players: Player[] = [
    {
    id: "1",
    display_name: "Player One",
    avatar_url: "/path/to/avatar1.png",
    stats: {},
    lives: 3,
    score: 120,
    is_spectator: false,
    has_answered: false,
    lastAnswer: "correct"
    },
    {
    id: "2",
    display_name: "Player Two",
    avatar_url: "/path/to/avatar2.png",
    stats: {},
    lives: 2,
    score: 80,
    is_spectator: false,
    has_answered: false,
    lastAnswer: "incorrect"
    },
    {
    id: "3",
    display_name: "Spectator",
    avatar_url: "/path/to/avatar3.png",
    stats: {},
    lives: 0,
    score: 40,
    is_spectator: true,
    has_answered: false,
    lastAnswer: "pending"
    }
];

export const timeLeft = 15;
export const roundNumber = 1;
export const isAudioPlaying = false;

export const getActivePlayers = () => {
    return players.filter((player) => (player.lives || 0) > 0 && !player.is_spectator);
};

export const getEliminatedPlayers = () => {
    return players.filter((player) => (player.lives || 0) <= 0 || player.is_spectator);
};

export const fetchPlayersFromDatabase = async (roomName: string, matchId: string) => {
    try {
        const difficulty = roomName.toLowerCase();
        const { data: { user }, error: userError} = await supabase.auth.getUser();

    if (userError) {
        toast.error("Failed to fetch user data.");
        console.error("Error fetching user:", userError);
    }

    // fetch all players in this match with their player profile data
    const { data: matchPlayersData, error: matchPlayersError } = await supabase
        .from('match_players')
        .select(`
            player_id,
            score,
            lastAnswer,
            lives,
            has_answered,
            is_spectator,
            players (
            id,
            display_name,
            avatar_url
            )
        `)
        .eq('match_id', matchId)

    if (matchPlayersError) {
        toast.error("Failed to fetch match players.");
        console.error("Error fetching match players:", matchPlayersError);
        return [];
    }

    if (matchPlayersData && matchPlayersData.length > 0) {
        // convert database format to ourp layer interface
            const formattedPlayers: Player[] = matchPlayersData.map((matchPlayer: any) => {
                const playerProfile = matchPlayer.players;

                return {
                    id: matchPlayer.player_id,
                    display_name: playerProfile.display_name || 'Anonymous',
                    avatar_url: playerProfile?.avatar_url || undefined,
                    stats: playerProfile?.stats || {},
                    lives: matchPlayer.lives,
                    score: matchPlayer.score,
                    is_spectator: matchPlayer.is_spectator,
                    has_answered: matchPlayer.has_answered,
                    lastAnswer: matchPlayer.lastAnswer
                }
            })

        return formattedPlayers;
    }

    } catch (error) {
        console.error("Error fetching players:", error);
    }
}

export const getCurrentWordForMatch = async (matchId?: string): Promise<currentWord> => {
    if (matchId) {
        const wordData = await getCurrentWord(matchId);
        if (wordData) {
            return {
                id: wordData.id,
                text: wordData.word,
                difficulty: wordData.difficulty,
                audioUrl: wordData.audio_clip_url || undefined
            };
        }
    }

    // Fallback to mock data if no match ID or word data not found
    return {
        id: 0,
        text: "",
        difficulty: "easy"
    };
}

export async function initiateSpellingTimer(
  matchId: string,
  setTimeLeft: (time: number) => void,
  setTimeLeftPrecise: (time: number) => void,
  setInitialTime: (time: number) => void,
  initialTimeInSeconds: number,
) {
  try {
    const { data: match, error: matchError} = await supabase
        .from('matches')
        .select('start_time, current_state')
        .eq('id', matchId)
        .single()

    if (matchError) {
        console.error("Error fetching waiting users:", matchError);
    }
    
    const currentStartTime = match?.start_time

    if (!currentStartTime) {
      const success = await attemptSetStartTime(matchId, initialTimeInSeconds);
      if (success) {
        setTimeLeft(calculateTimeLeft(success));
        setTimeLeftPrecise(calculateTimeLeft(success) * 1000);
        setInitialTime(initialTimeInSeconds);        
      }
    } else if (currentStartTime) {
      setTimeLeftPrecise(calculateTimeLeft(currentStartTime) * 1000);
      setInitialTime(initialTimeInSeconds);
    }
  } catch (error) {
    console.error("Exception fetching waiting users:", error);
  }
}

async function attemptSetStartTime(matchId: string, initialTimeInSeconds: number) {
  try {
    const { data: currentMatch, error: fetchError } = await supabase
      .from('matches')
      .select('start_time')
      .eq('id', matchId)
      .single();

    if (fetchError || !currentMatch) {
      console.error("Error fetching current match:", fetchError);
      return false;
    }

    const startTime = new Date();
    startTime.setSeconds(startTime.getSeconds() + initialTimeInSeconds);
    const startTimeISO = startTime.toISOString();

    const { error: updateError } = await supabase
      .from('matches')
      .update({ start_time: startTimeISO })
      .eq('id', matchId)
      .is('start_time', null)
      .select();

    if (updateError) {
      console.error("Error setting start time:", updateError);
      return false;
    }

    return startTimeISO;
  } catch (error) {
    console.error("Exception setting start time:", error);
    return false;
  }
}

export async function performHeartbeatSyncBattle(
  supabase: ReturnType<typeof createClient>,
  currentMatchId: string | null,
  setTimeLeft: (time: number) => void,
  setTimeLeftPrecise: (time: number) => void,
  initialTimeInSeconds: number,
  initialBreakTimeInSeconds: number
) {
  try {
    if (!currentMatchId) return;
    const { data: currentMatch, error } = await supabase
      .from('matches')
      .select('id, status, start_time')
      .eq('id', currentMatchId)
      .single();

    if (error || !currentMatch) {
      console.error("Heartbeat sync error:", error);
      return;
    }

    const now = new Date();
    const startTime = currentMatch.start_time ? new Date(currentMatch.start_time) : null;

    if (startTime) {
        const newTimeLeft = Math.max(0, Math.floor((startTime.getTime() - now.getTime()) / 1000));
        console.log(newTimeLeft);
        setTimeLeft(newTimeLeft);
        setTimeLeftPrecise(newTimeLeft * 1000);
    } 
  } catch (error) {
    console.error("Exception in heartbeat sync:", error);
  }
}

export async function updatePendingPlayers(matchId: string) {
  try {
    const { data: matchPlayers, error } = await supabase
      .from('match_players')
      .select('player_id, lastAnswer, lives, has_answered')
      .eq('match_id', matchId);

    if (error) {
      console.error("Error fetching match players for update:", error);
      return;
    }

    if (matchPlayers && matchPlayers.length > 0) {
      const updates = matchPlayers
        .filter(player => player.lastAnswer === "pending")
        .map(player => ({
          match_id: matchId, 
          player_id: player.player_id,
          lastAnswer: "incorrect",
          lives: (player.lives || 0) - 1,
        }));

      if (updates.length > 0) {
        const { error: updateError } = await supabase
          .from('match_players')
          .upsert(updates, { onConflict: 'match_id,player_id' });

        if (updateError) {
          console.error("Error updating pending players:", updateError);
        }
      }
    }
  } catch (error) {
    console.error("Exception in updatePendingPlayers:", error);
  }
}

export async function updatePlayersHasAnswered(matchId: string) {
  const { error } = await supabase
    .from('match_players')
    .update({ has_answered: false })
    .eq('match_id', matchId)

  if (error) {
    console.error(error)
  }
}
export async function updateAllPlayersToPending(matchId: string) {
  const { data, error: matchesError } = await supabase
    .from('matches')
    .select('is_locked')
    .eq('id', matchId)
    .single()

    if (matchesError) {
      console.error(matchesError)
    }

  if (data?.is_locked) {
    const { error: playersError } = await supabase
      .from('match_players')
      .update({ lastAnswer: "pending" })
      .eq('match_id', matchId)

    if (playersError) {
      console.error(playersError)
    }
  }
}

export const transitionToBreak = async (
  matchId: string,
  setTimeLeft: (time: number) => void,
  setTimeLeftPrecise: (time: number) => void,
  setInitialTime: (time: number) => void,
  initialBreakTimeInSeconds: number,
  clientCurrentWordId: number,
  setCurrentRound: (round: number) => void,
  setIsAudioPlaying: (isPlaying: boolean) => void,
  setBreakTimerStart: (isStarted: boolean) => void,
  clearTimer: () => void,
  shouldEndMatch: () => boolean,
  endMatch: () => Promise<void>,
  currentRound: number
) => {
  if (!matchId) return;
  console.log("All players were answered");
  clearTimer();

  if (shouldEndMatch()) {
    await endMatch();
  }
  
  await updateMatchCurrentState(matchId, "spelling", "break");
  await updatePendingPlayers(matchId);
  await updatePlayersHasAnswered(matchId);
  await resetStartTime(matchId);

  await initiateSpellingTimer(
    matchId,
    setTimeLeft,
    setTimeLeftPrecise,
    setInitialTime,
    initialBreakTimeInSeconds
  );

  const pickedWordId = await setCurrentWordId(matchId, clientCurrentWordId);
  console.log(`Picked ID: ${pickedWordId}`)

  // Increment round number in database
  const { error: roundError } = await supabase
    .from('matches')
    .update({ current_round: currentRound + 1 })
    .eq('id', matchId);

  if (!roundError) {
    setCurrentRound(currentRound + 1);
  }

  // setClientGameState("break");
  setIsAudioPlaying(true);
  setBreakTimerStart(true);
}

export const transitionToSpelling = async (
  matchId: string,
  setBreakTimerStart: (isStarted: boolean) => void,
  playAudio: () => Promise<currentWord>,
  clearTimer: () => void,
  shouldEndMatch: () => boolean,
  endMatch: () => Promise<void>,
) => {
  if (!matchId) return;
  clearTimer();

  if (shouldEndMatch()) {
    await endMatch();
  }
  
  await resetStartTime(matchId);
  await updateMatchCurrentState(matchId, "break", "spelling");
  await updateAllPlayersToPending(matchId);
  await setMatchLock(matchId, false);
  setBreakTimerStart(false);
  await playAudio();
}